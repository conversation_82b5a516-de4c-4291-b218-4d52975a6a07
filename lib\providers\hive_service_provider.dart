import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/interfaces/hive_service_interface.dart';
import '../services/implementations/hive_service_impl.dart';

/// Provider for the HiveService
final hiveServiceProvider = Provider<HiveServiceInterface>((ref) {
  final hiveService = HiveServiceImpl();
  
  // Initialize Hive when the provider is first accessed
  hiveService.init();
  
  return hiveService;
});
