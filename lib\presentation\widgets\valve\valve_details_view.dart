import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../core/widgets/detail_item.dart';
import '../../../core/widgets/status_badge.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../domain/entities/valve.dart';
import '../../../utils/nepali_names.dart';

/// A widget for displaying valve details
class ValveDetailsView extends ConsumerWidget {
  final Valve valve;
  final VoidCallback? onEdit;

  const ValveDetailsView({
    super.key,
    required this.valve,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get a Nepali name for the valve
    final displayName = NepaliFriendlyNames.getNameByType('valve', valve.title);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomCard(
          child: ListTile(
            leading: Icon(Icons.plumbing,
                size: 40, color: _getStatusColor(valve.status)),
            title: Text(
              displayName,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            subtitle:
                Text('Type: ${valve.specifications['type'] ?? 'Standard'}'),
            trailing: StatusBadge.valve(valve.status),
            onTap: onEdit,
          ),
        ),
        const SizedBox(height: 16),
        DetailItem(
          label: 'Price Per Unit',
          value: valve.pricePerUnit != null
              ? '${valve.pricePerUnit!.toStringAsFixed(2)} NPR'
              : 'Not set',
        ),
        DetailItem(
          label: 'Control Type',
          value: valve.specifications['controlType'] ?? 'Manual',
        ),
        DetailItem(
          label: 'Location',
          value:
              'Lat: ${valve.latitude.toStringAsFixed(6)}, Lng: ${valve.longitude.toStringAsFixed(6)}',
        ),
        DetailItem(
          label: 'ID',
          value: valve.id,
        ),
        DetailItem(
          label: 'Created At',
          value: _formatTimestamp(valve.createdAt.millisecondsSinceEpoch),
        ),
        DetailItem(
          label: 'Updated At',
          value: _formatTimestamp(valve.updatedAt.millisecondsSinceEpoch),
        ),
        if (valve.lastMaintenance != null)
          DetailItem(
            label: 'Last Maintenance',
            value:
                _formatTimestamp(valve.lastMaintenance!.millisecondsSinceEpoch),
          ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Open':
        return Colors.green;
      case 'Closed':
        return Colors.red;
      case 'Maintenance':
        return Colors.orange;
      default:
        return Colors.black;
    }
  }

  String _formatTimestamp(int timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'Invalid date';
    }
  }
}
