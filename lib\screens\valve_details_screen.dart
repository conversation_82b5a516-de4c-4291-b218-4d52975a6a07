import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/hive_models/hive_valve_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';
import '../utils/nepali_names.dart';

class ValveDetailsScreen extends ConsumerStatefulWidget {
  final HiveValveModel valve;

  const ValveDetailsScreen({
    super.key,
    required this.valve,
  });

  @override
  ConsumerState<ValveDetailsScreen> createState() => _ValveDetailsScreenState();
}

class _ValveDetailsScreenState extends ConsumerState<ValveDetailsScreen> {
  late TextEditingController _titleController;
  late TextEditingController _typeController;
  late TextEditingController _priceController;
  late String _selectedStatus;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final valve = widget.valve;
    final specs = valve.specifications;

    _titleController = TextEditingController(text: valve.title);
    _typeController =
        TextEditingController(text: specs['type'] as String? ?? 'Standard');
    _priceController = TextEditingController(
      text: valve.pricePerUnit?.toString() ?? '',
    );
    _selectedStatus = valve.status;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _typeController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  Future<void> _syncValve() async {
    final firestoreRepository = ref.read(firestoreRepositoryProvider);
    final hiveService = ref.read(hiveServiceProvider);

    // Create LatLng position
    final position = LatLng(widget.valve.latitude, widget.valve.longitude);

    // Save to Firestore elements collection with the correct parameters
    await firestoreRepository.addElement('valve', position, widget.valve.areaId,
        widget.valve.title, {'type': 'valve'});

    // Save to Firestore valves collection
    await firestoreRepository.addValveDetails(
        widget.valve.id, widget.valve.specifications);

    // Mark as synced in Hive
    widget.valve.isSync = true;
    await hiveService.saveValve(widget.valve);
  }

  Future<void> _saveChanges() async {
    final hiveService = ref.read(hiveServiceProvider);
    final firestoreRepository = ref.read(firestoreRepositoryProvider);

    // Update the valve
    widget.valve.title = _titleController.text;
    widget.valve.specifications = {
      'type': _typeController.text,
    };
    widget.valve.status = _selectedStatus;
    widget.valve.updatedAt = DateTime.now();
    widget.valve.isSync = false;

    // Update price per unit
    if (_priceController.text.isNotEmpty) {
      widget.valve.pricePerUnit = double.tryParse(_priceController.text);
    } else {
      widget.valve.pricePerUnit = null;
    }

    try {
      // Save to Hive
      await hiveService.saveValve(widget.valve);

      // Save to Firestore if already synced
      if (widget.valve.isSync) {
        // Update in Firestore elements collection
        await firestoreRepository.updateElementTitle(
            widget.valve.id, widget.valve.title);

        // Update in Firestore valves collection
        await firestoreRepository.updateValveDetails(
            widget.valve.id, widget.valve.specifications);

        // Mark as synced in Hive
        widget.valve.isSync = true;
        await hiveService.saveValve(widget.valve);
      }

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show success message
      _showSnackBar('Valve "${_titleController.text}" updated successfully');

      // Exit edit mode
      setState(() {
        _isEditing = false;
      });
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error message
      _showSnackBar('Error updating valve: $e', isError: true);
    }
  }

  Future<void> _deleteValve() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Valve'),
        content:
            Text('Are you sure you want to delete "${widget.valve.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    try {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Mark as deleted in Hive
      final valve = widget.valve;
      valve.isDeleted = true;
      valve.isSync = false;
      await hiveService.saveValve(valve);

      // Delete from Firestore if it was synced
      if (valve.isSync) {
        await firestoreRepository.deleteElement(valve.id);
      }

      // Permanently delete from Hive
      await hiveService.deleteValve(valve.id, permanent: true);

      // Show success message
      if (!mounted) return;
      _showSnackBar('Valve "${valve.title}" deleted successfully');

      // Navigate back
      Navigator.pop(context, true); // Pass true to indicate deletion
    } catch (e) {
      if (!mounted) return;
      _showSnackBar('Error deleting valve: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Valve' : 'Valve Details'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            onPressed: _isEditing ? _saveChanges : _toggleEditMode,
          ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteValve,
            ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                setState(() {
                  // Refresh the data
                  final hiveService = ref.read(hiveServiceProvider);
                  final valves = hiveService.getAllValves();
                  final updatedValve = valves.firstWhere(
                    (v) => v.id == widget.valve.id,
                    orElse: () => widget.valve,
                  );

                  if (updatedValve != widget.valve) {
                    // Update the widget's valve with the latest data
                    widget.valve.title = updatedValve.title;
                    widget.valve.specifications = updatedValve.specifications;
                    widget.valve.status = updatedValve.status;
                    widget.valve.isSync = updatedValve.isSync;
                    widget.valve.updatedAt = updatedValve.updatedAt;
                  }
                });
              },
            ),
          if (!_isEditing)
            IconButton(
              icon: Icon(
                widget.valve.isSync ? Icons.cloud_done : Icons.cloud_upload,
                color: widget.valve.isSync ? Colors.green : Colors.orange,
              ),
              onPressed: () {
                if (widget.valve.isSync) {
                  _showSnackBar('Valve is already synced');
                  return;
                }

                // Use a separate method to handle the async operations
                _syncValve().then((_) {
                  if (mounted) {
                    setState(() {}); // Refresh UI
                    _showSnackBar('Valve synced successfully');
                  }
                }).catchError((e) {
                  if (mounted) {
                    _showSnackBar('Error syncing valve: $e', isError: true);
                  }
                });
              },
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: _isEditing ? _buildEditForm() : _buildDetailsView(),
      ),
    );
  }

  Widget _buildDetailsView() {
    final valve = widget.valve;
    final specs = valve.specifications;

    // Determine color based on status
    Color statusColor;
    switch (valve.status) {
      case 'Open':
        statusColor = Colors.green;
        break;
      case 'Closed':
        statusColor = Colors.red;
        break;
      case 'Maintenance':
        statusColor = Colors.orange;
        break;
      default:
        statusColor = Colors.black;
    }

    // Get a Nepali name for the valve
    final displayName = NepaliFriendlyNames.getNameByType('valve', valve.title);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: ListTile(
            leading: Icon(Icons.plumbing, size: 40, color: statusColor),
            title: Text(displayName,
                style: Theme.of(context).textTheme.titleLarge),
            subtitle: Text('Type: ${specs['type'] ?? 'Standard'}'),
            trailing: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: statusColor.withAlpha(51), // 0.2 * 255 = 51
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                valve.status,
                style:
                    TextStyle(color: statusColor, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        _buildDetailItem(
            'Price Per Unit',
            valve.pricePerUnit != null
                ? '${valve.pricePerUnit!.toStringAsFixed(2)} NPR'
                : 'Not set'),
        _buildDetailItem('Location',
            'Lat: ${valve.latitude.toStringAsFixed(6)}, Lng: ${valve.longitude.toStringAsFixed(6)}'),
        _buildDetailItem('ID', valve.id),
        _buildDetailItem('Created At',
            _formatTimestamp(valve.createdAt.millisecondsSinceEpoch)),
        _buildDetailItem('Updated At',
            _formatTimestamp(valve.updatedAt.millisecondsSinceEpoch)),
        if (valve.lastMaintenance != null)
          _buildDetailItem('Last Maintenance',
              _formatTimestamp(valve.lastMaintenance!.millisecondsSinceEpoch)),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
          const Divider(),
        ],
      ),
    );
  }

  String _formatTimestamp(int timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  Widget _buildEditForm() {
    return Form(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Valve Name *',
              hintText: 'Enter valve name',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _typeController,
            decoration: const InputDecoration(
              labelText: 'Valve Type *',
              hintText: 'e.g., Gate, Ball, Globe, Butterfly',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _priceController,
            decoration: const InputDecoration(
              labelText: 'Price Per Unit (NPR)',
              hintText: 'e.g., 500.00',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedStatus,
            decoration: const InputDecoration(
              labelText: 'Status',
            ),
            items: const [
              DropdownMenuItem(value: 'Open', child: Text('Open')),
              DropdownMenuItem(value: 'Closed', child: Text('Closed')),
              DropdownMenuItem(
                  value: 'Maintenance', child: Text('Maintenance')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedStatus = value;
                });
              }
            },
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                    _initializeControllers(); // Reset to original values
                  });
                },
                icon: const Icon(Icons.cancel),
                label: const Text('Cancel'),
              ),
              ElevatedButton.icon(
                onPressed: _saveChanges,
                icon: const Icon(Icons.save),
                label: const Text('Save Changes'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
