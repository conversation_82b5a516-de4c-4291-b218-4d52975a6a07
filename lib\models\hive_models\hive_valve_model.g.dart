// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_valve_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveValveModelAdapter extends TypeAdapter<HiveValveModel> {
  @override
  final int typeId = 2;

  @override
  HiveValveModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveValveModel(
      id: fields[0] as String?,
      createdAt: fields[1] as DateTime?,
      updatedAt: fields[2] as DateTime?,
      isSync: fields[3] as bool,
      isDeleted: fields[4] as bool,
      latitude: fields[5] as double,
      longitude: fields[6] as double,
      title: fields[7] as String,
      specifications: (fields[8] as Map?)?.cast<String, dynamic>(),
      status: fields[9] as String?,
      lastMaintenance: fields[10] as DateTime?,
      areaId: fields[11] as String,
      pricePerUnit: fields[12] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, HiveValveModel obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.createdAt)
      ..writeByte(2)
      ..write(obj.updatedAt)
      ..writeByte(3)
      ..write(obj.isSync)
      ..writeByte(4)
      ..write(obj.isDeleted)
      ..writeByte(5)
      ..write(obj.latitude)
      ..writeByte(6)
      ..write(obj.longitude)
      ..writeByte(7)
      ..write(obj.title)
      ..writeByte(8)
      ..write(obj.specifications)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.lastMaintenance)
      ..writeByte(11)
      ..write(obj.areaId)
      ..writeByte(12)
      ..write(obj.pricePerUnit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveValveModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
