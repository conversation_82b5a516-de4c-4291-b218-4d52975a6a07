import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../models/firestore_models.dart';
import '../../providers/firestore_provider.dart';
import '../../home/<USER>';
import '../../repositories/firestore_repository.dart';

class ValveDetailsEditor extends ConsumerStatefulWidget {
  final String valveId;
  final String title;
  final Function? onSaved;

  const ValveDetailsEditor({
    Key? key,
    required this.valveId,
    required this.title,
    this.onSaved,
  }) : super(key: key);

  @override
  _ValveDetailsEditorState createState() => _ValveDetailsEditorState();
}

class _ValveDetailsEditorState extends ConsumerState<ValveDetailsEditor> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  
  // Form fields
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _typeController = TextEditingController();
  String _status = 'Operational';
  DateTime? _lastMaintenance;
  final TextEditingController _diameterController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  final List<String> _statusOptions = [
    'Operational',
    'Maintenance Required',
    'Out of Service',
    'Unknown'
  ];

  @override
  void initState() {
    super.initState();
    _loadValveDetails();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _typeController.dispose();
    _diameterController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadValveDetails() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      final valveDetails = await firestoreRepository.getElementDetails(
          widget.valveId, 'valve') as FirestoreValveDetails?;

      if (valveDetails != null) {
        _nameController.text = widget.title;
        _typeController.text = valveDetails.specifications['type'] ?? 'Standard';
        _status = valveDetails.status;
        _lastMaintenance = valveDetails.lastMaintenance;
        _diameterController.text = 
            (valveDetails.specifications['diameter'] ?? '').toString();
        _notesController.text = valveDetails.specifications['notes'] ?? '';
      } else {
        _nameController.text = widget.title;
        _typeController.text = 'Standard';
        _status = 'Operational';
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error loading valve details: $e';
      });
      print('Error loading valve details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveValveDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      
      // Create specifications map
      final specifications = {
        'type': _typeController.text,
        'status': _status,
        'diameter': _diameterController.text.isNotEmpty 
            ? int.tryParse(_diameterController.text) ?? 0 
            : 0,
        'notes': _notesController.text,
        'lastMaintenance': _lastMaintenance,
      };

      // Update valve details
      await firestoreRepository.updateValveDetails(
          widget.valveId, specifications);
      
      // Update title in elements collection if name changed
      if (_nameController.text != widget.title) {
        await firestoreRepository.updateElementTitle(
            widget.valveId, _nameController.text);
      }

      // Call onSaved callback if provided
      if (widget.onSaved != null) {
        widget.onSaved!();
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Valve details saved successfully')),
      );

      // Close the dialog
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Error saving valve details: $e';
      });
      print('Error saving valve details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Valve Details'),
        actions: [
          IconButton(
            icon: Icon(Icons.save),
            onPressed: _isLoading ? null : _saveValveDetails,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _hasError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 10),
                      Text(_errorMessage),
                      SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: _loadValveDetails,
                        child: Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: _nameController,
                          decoration: InputDecoration(
                            labelText: 'Name',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a name';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _typeController,
                          decoration: InputDecoration(
                            labelText: 'Valve Type',
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: _status,
                          decoration: InputDecoration(
                            labelText: 'Status',
                            border: OutlineInputBorder(),
                          ),
                          items: _statusOptions.map((String status) {
                            return DropdownMenuItem<String>(
                              value: status,
                              child: Text(status),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null) {
                              setState(() {
                                _status = newValue;
                              });
                            }
                          },
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _diameterController,
                          decoration: InputDecoration(
                            labelText: 'Diameter (mm)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'Last Maintenance: ${_lastMaintenance != null ? _formatDate(_lastMaintenance!) : 'Not set'}',
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                            TextButton(
                              onPressed: () => _selectDate(context),
                              child: Text('Select Date'),
                            ),
                            if (_lastMaintenance != null)
                              IconButton(
                                icon: Icon(Icons.clear),
                                onPressed: () {
                                  setState(() {
                                    _lastMaintenance = null;
                                  });
                                },
                              ),
                          ],
                        ),
                        SizedBox(height: 16),
                        TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'Notes',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _lastMaintenance ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _lastMaintenance) {
      setState(() {
        _lastMaintenance = picked;
      });
    }
  }
}
