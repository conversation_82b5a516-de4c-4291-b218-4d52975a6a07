import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart'; // Keep for GeoPoint type

import '../models/hive_models/hive_user_model.dart';
import '../models/hive_models/hive_valve_model.dart';
import '../models/hive_models/hive_connector_model.dart';
import '../models/hive_models/hive_pipeline_model.dart';
import '../models/firestore_models.dart';
import '../repositories/firestore_repository.dart';
import 'connectivity_service.dart';
import 'hive_service.dart';

class SyncService {
  final FirestoreRepository _firestoreRepository;
  final ConnectivityService _connectivityService;

  // Stream controller for sync status
  late final StreamController<SyncStatus> _syncStatusController;
  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;

  // Current sync status
  SyncStatus _status = SyncStatus.idle;
  SyncStatus get status => _status;

  // Sync statistics
  int _totalItems = 0;
  int _syncedItems = 0;
  int _failedItems = 0;

  SyncService({
    required FirestoreRepository firestoreRepository,
    required ConnectivityService connectivityService,
  })  : _firestoreRepository = firestoreRepository,
        _connectivityService = connectivityService {
    // Initialize the stream controller
    _syncStatusController = StreamController<SyncStatus>.broadcast();

    // Check for unsynced items and update status accordingly
    _updateStatusBasedOnUnsyncedItems();

    // Listen for changes in Hive boxes
    _setupHiveListeners();

    debugPrint('SyncService initialized with status: $_status');
  }

  // Timer for periodic checks
  Timer? _checkTimer;

  // Setup listeners for Hive boxes to update status when data changes
  void _setupHiveListeners() {
    // We'll use a periodic timer to check for changes instead of box listeners
    // since listenable() is not available in the current Hive version
    _checkTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      debugPrint('SyncService: Checking for unsynced items');
      _updateStatusBasedOnUnsyncedItems();
    });
  }

  // We no longer need the _isFirstLaunch flag since we only fetch from Firestore
  // when the database is empty (fresh installation)

  // Check if data should be fetched from Firestore
  // This is used ONLY when the app is first installed (empty database)
  Future<bool> shouldFetchFromFirestore() async {
    // Only return true if the local database is empty - this is the most reliable
    // way to detect a fresh installation or reinstallation
    if (HiveService.isDatabaseEmpty()) {
      debugPrint('🔍 Local database is empty, should fetch from Firestore');

      // Check if we have connectivity
      if (!await _connectivityService.checkConnectivity()) {
        debugPrint(
            '⚠️ No connectivity with empty database, cannot fetch from Firestore');
        return false;
      }

      // Database is empty, so we should fetch data
      return true;
    }

    // If database is not empty, don't fetch from Firestore automatically
    // Data should only be synced when the sync button is pressed
    debugPrint(
        '🔍 Local database is not empty, should NOT fetch from Firestore');
    debugPrint('🔍 Data will only be synced when the sync button is pressed');

    // Database is not empty, so we should not fetch data

    return false; // Only fetch if database is empty (fresh installation)
  }

  // Force a refresh from Firestore - useful when the app is reinstalled
  // or when we need to ensure we have the latest data
  Future<bool> forceRefreshFromFirestore() async {
    debugPrint('🔄 Forcing refresh from Firestore...');

    // Check connectivity first
    if (!await _connectivityService.checkConnectivity()) {
      debugPrint('⚠️ No connectivity, cannot force refresh from Firestore');
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    // Clear local database to ensure we get fresh data
    debugPrint('🧹 Clearing local database before force refresh');
    await HiveService.clearAllData();

    // Database is now empty, so we should fetch data

    // Fetch all data from Firestore
    debugPrint('🔄 Fetching all data from Firestore after force refresh');
    return await fetchAllDataFromFirestore();
  }

  // Fetch all data from Firestore (used when local database is empty or app is first opened)
  Future<bool> fetchAllDataFromFirestore() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      debugPrint('⚠️ No connectivity, cannot fetch data from Firestore');
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);
    debugPrint('🔄 Starting to fetch all data from Firestore...');

    try {
      // Clear any existing data if this is a fresh installation
      if (HiveService.isDatabaseEmpty()) {
        debugPrint(
            '🧹 Database is empty, ensuring it\'s clean before fetching');
        await HiveService.clearAllData();
      }

      // Fetch all users
      debugPrint('👤 Fetching users from elements collection...');
      final allUsers = await _firestoreRepository.getAllElements('user');
      debugPrint('✅ Found ${allUsers.length} users in elements collection');

      // Only process users from elements collection
      // We don't want to automatically add users from user collection to elements collection
      if (allUsers.isEmpty) {
        debugPrint('⚠️ No users found in elements collection');
      } else {
        // Process users from elements collection
        int usersSaved = 0;
        int userErrors = 0;
        debugPrint(
            '🔄 Processing ${allUsers.length} users from elements collection...');
        for (final userDoc in allUsers) {
          try {
            final element = FirestoreElement.fromFirestore(userDoc);
            final hiveUser =
                _firestoreRepository.createHiveUserFromFirestore(element);
            await HiveService.saveUser(hiveUser, markAsSynced: true);
            usersSaved++;
          } catch (e) {
            debugPrint('❌ Error processing user from elements collection: $e');
            userErrors++;
          }
        }
        debugPrint(
            '✅ Processed users from elements collection: $usersSaved saved, $userErrors errors');
      }

      // Fetch all valves
      debugPrint('🔄 Fetching valves from elements collection...');
      final allValves = await _firestoreRepository.getAllElements('valve');
      debugPrint('✅ Found ${allValves.length} valves in elements collection');

      int valvesSaved = 0;
      int valveErrors = 0;
      for (final valveDoc in allValves) {
        try {
          final element = FirestoreElement.fromFirestore(valveDoc);
          final hiveValve =
              _firestoreRepository.createHiveValveFromFirestore(element);
          await HiveService.saveValve(hiveValve, markAsSynced: true);
          valvesSaved++;
        } catch (e) {
          debugPrint('❌ Error processing valve: $e');
          valveErrors++;
        }
      }
      debugPrint('✅ Processed valves: $valvesSaved saved, $valveErrors errors');

      // Fetch all connectors
      debugPrint('🔄 Fetching connectors from elements collection...');
      final allConnectors =
          await _firestoreRepository.getAllElements('connector');
      debugPrint(
          '✅ Found ${allConnectors.length} connectors in elements collection');

      int connectorsSaved = 0;
      int connectorErrors = 0;
      for (final connectorDoc in allConnectors) {
        try {
          final element = FirestoreElement.fromFirestore(connectorDoc);
          final hiveConnector =
              _firestoreRepository.createHiveConnectorFromFirestore(element);
          await HiveService.saveConnector(hiveConnector, markAsSynced: true);
          connectorsSaved++;
        } catch (e) {
          debugPrint('❌ Error processing connector: $e');
          connectorErrors++;
        }
      }
      debugPrint(
          '✅ Processed connectors: $connectorsSaved saved, $connectorErrors errors');

      // Fetch all pipelines from elements collection
      debugPrint('🔄 Fetching pipelines from elements collection...');
      final allPipelineElements =
          await _firestoreRepository.getAllElements('pipeline');
      debugPrint(
          '✅ Found ${allPipelineElements.length} pipeline elements in elements collection');

      // Fetch all pipelines from pipelines collection
      debugPrint('🔄 Fetching pipelines from pipelines collection...');
      final allPipelines = await _firestoreRepository.getAllPipelines();
      debugPrint(
          '✅ Found ${allPipelines.length} pipelines in pipelines collection');

      // If no pipelines found in either collection, create a sample pipeline
      if (allPipelineElements.isEmpty && allPipelines.isEmpty) {
        debugPrint(
            '⚠️ No pipelines found in Firestore, creating a sample pipeline');

        // Create a sample pipeline with some segments
        try {
          // Create sample points (adjust these coordinates as needed)
          final points = [
            const LatLng(
                27.700769, 85.300140), // Sample coordinates in Kathmandu
            const LatLng(27.701769, 85.301140),
            const LatLng(27.702769, 85.302140),
          ];

          // Add the pipeline to Firestore
          await _firestoreRepository.addPipelineElement(
            points,
            'default', // areaId
            'Sample Pipeline', // title
            state: 'Active',
          );

          debugPrint('✅ Created sample pipeline in Firestore');
        } catch (e) {
          debugPrint('❌ Error creating sample pipeline: $e');
        }
      }

      // Process pipeline elements first
      int pipelinesSaved = 0;
      int pipelineErrors = 0;
      debugPrint(
          '🔄 Processing ${allPipelineElements.length} pipelines from elements collection...');
      for (final pipelineDoc in allPipelineElements) {
        try {
          debugPrint('🔄 Processing pipeline document ${pipelineDoc.id}');

          // Check if document data is null
          if (pipelineDoc.data() == null) {
            debugPrint(
                '⚠️ Document data is null for pipeline ${pipelineDoc.id}');
            continue;
          }

          // Log document data for debugging
          final data = pipelineDoc.data() as Map<String, dynamic>;
          debugPrint('📄 Pipeline document data: ${data.keys.join(', ')}');

          if (data.containsKey('path')) {
            final path = data['path'];
            if (path == null) {
              debugPrint('⚠️ Path is null for pipeline ${pipelineDoc.id}');
            } else if (path is List) {
              debugPrint('✅ Path is a list with ${path.length} points');
            } else {
              debugPrint('⚠️ Path is not a list: ${path.runtimeType}');
            }
          } else {
            debugPrint('⚠️ No path field found for pipeline ${pipelineDoc.id}');
          }

          if (data.containsKey('metadata')) {
            final metadata = data['metadata'];
            if (metadata == null) {
              debugPrint('⚠️ Metadata is null for pipeline ${pipelineDoc.id}');
            } else if (metadata is Map) {
              debugPrint('✅ Metadata is a map with ${metadata.length} entries');
            } else {
              debugPrint('⚠️ Metadata is not a map: ${metadata.runtimeType}');
            }
          } else {
            debugPrint(
                '⚠️ No metadata field found for pipeline ${pipelineDoc.id}');
          }

          final pipeline = FirestorePipeline.fromFirestore(pipelineDoc);
          debugPrint('✅ Successfully created FirestorePipeline from document');

          final hivePipeline = await _firestoreRepository
              .createHivePipelineFromFirestore(pipeline);
          debugPrint(
              '✅ Successfully created HivePipelineModel from FirestorePipeline');

          await HiveService.savePipeline(hivePipeline, markAsSynced: true);
          debugPrint(
              '✅ Saved pipeline ${pipeline.id} from elements collection');
          pipelinesSaved++;
        } catch (e) {
          debugPrint('❌ Error processing pipeline element: $e');
          pipelineErrors++;
        }
      }
      debugPrint(
          '✅ Processed pipelines from elements: $pipelinesSaved saved, $pipelineErrors errors');

      // Then process pipelines from pipelines collection to get full details
      int pipelinesUpdated = 0;
      int pipelineUpdateErrors = 0;
      debugPrint(
          '🔄 Processing ${allPipelines.length} pipelines from pipelines collection...');
      for (final pipelineDoc in allPipelines) {
        try {
          final pipelineData = pipelineDoc.data() as Map<String, dynamic>;
          final pipelineId = pipelineDoc.id;

          // Check if we already have this pipeline in local storage
          final existingPipeline = HiveService.getPipeline(pipelineId);
          if (existingPipeline != null) {
            // Update the existing pipeline with full details
            debugPrint(
                '🔄 Updating existing pipeline $pipelineId with full details');

            // Get segment states from the pipeline document
            Map<String, String> segmentStates = {};
            if (pipelineData.containsKey('segments')) {
              final segments = pipelineData['segments'] as List<dynamic>;
              for (final segment in segments) {
                final segmentMap = segment as Map<String, dynamic>;
                final segmentId = segmentMap['id'] as String;
                final segmentState = segmentMap['state'] as String? ?? 'Active';
                segmentStates[segmentId] = segmentState;
              }
            }

            // Update the pipeline properties with segment states
            if (segmentStates.isNotEmpty) {
              existingPipeline.properties['segmentStates'] = segmentStates;
              debugPrint(
                  '✅ Added ${segmentStates.length} segment states to pipeline $pipelineId');
            }

            // Save the updated pipeline
            existingPipeline.markAsSynced();
            await HiveService.savePipeline(existingPipeline,
                markAsSynced: true);
            pipelinesUpdated++;
          }
        } catch (e) {
          debugPrint(
              '❌ Error processing pipeline from pipelines collection: $e');
          pipelineUpdateErrors++;
        }
      }
      debugPrint(
          '✅ Updated pipelines from collection: $pipelinesUpdated updated, $pipelineUpdateErrors errors');

      // Verify data was loaded correctly
      final usersInHive = HiveService.getAllUsers().length;
      final valvesInHive = HiveService.getAllValves().length;
      final connectorsInHive = HiveService.getAllConnectors().length;
      final pipelinesInHive = HiveService.getAllPipelines().length;

      debugPrint(
          '📊 Data loaded into Hive: $usersInHive users, $valvesInHive valves, '
          '$connectorsInHive connectors, $pipelinesInHive pipelines');

      if (usersInHive == 0 &&
          valvesInHive == 0 &&
          connectorsInHive == 0 &&
          pipelinesInHive == 0) {
        debugPrint('⚠️ Warning: No data was loaded into Hive from Firestore!');
        // If we have data in Firestore but nothing was loaded, something went wrong
        if (allUsers.isNotEmpty ||
            allValves.isNotEmpty ||
            allConnectors.isNotEmpty ||
            allPipelineElements.isNotEmpty) {
          debugPrint(
              '❌ Error: Firestore has data but nothing was loaded into Hive');
          _updateStatus(SyncStatus.failed);
          return false;
        }
      }

      debugPrint('✅ Successfully fetched all data from Firestore');
      _updateStatus(SyncStatus.upToDate);
      return true;
    } catch (e) {
      debugPrint('❌ Error fetching all data from Firestore: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Start synchronization
  Future<bool> syncAll() async {
    // Check connectivity
    if (!await _connectivityService.checkConnectivity()) {
      _updateStatus(SyncStatus.noConnection);
      return false;
    }

    _updateStatus(SyncStatus.syncing);

    try {
      // First, fetch any new data from Firestore
      await _fetchNewDataFromFirestore();

      // Then proceed with uploading local changes
      final unsyncedItems = HiveService.getAllUnsyncedItems();

      // Debug logging for unsynced items
      debugPrint('🔄 Found ${unsyncedItems.length} unsynced items');

      // Count by type
      int userCount = 0;
      int valveCount = 0;
      int connectorCount = 0;
      int pipelineCount = 0;
      int otherCount = 0;

      for (final item in unsyncedItems) {
        if (item is HiveUserModel) {
          userCount++;
        } else if (item is HiveValveModel) {
          valveCount++;
        } else if (item is HiveConnectorModel) {
          connectorCount++;
        } else if (item is HivePipelineModel) {
          pipelineCount++;
        } else {
          otherCount++;
        }
      }

      debugPrint('🔄 Unsynced items breakdown:');
      debugPrint('👤 Users: $userCount');
      debugPrint('🔧 Valves: $valveCount');
      debugPrint('🔌 Connectors: $connectorCount');
      debugPrint('🚿 Pipelines: $pipelineCount');
      debugPrint('📊 Other items: $otherCount');

      if (unsyncedItems.isEmpty) {
        _updateStatus(SyncStatus.upToDate);
        return true;
      }

      // Log what we're syncing
      debugPrint('Syncing ${unsyncedItems.length} items to Firestore');
      for (final item in unsyncedItems) {
        if (item is HiveUserModel) {
          debugPrint('- User: ${item.id} (deleted: ${item.isDeleted})');
        } else if (item is HiveValveModel) {
          debugPrint('- Valve: ${item.id} (deleted: ${item.isDeleted})');
        } else if (item is HiveConnectorModel) {
          debugPrint('- Connector: ${item.id} (deleted: ${item.isDeleted})');
        } else if (item is HivePipelineModel) {
          debugPrint('- Pipeline: ${item.id} (deleted: ${item.isDeleted})');
        }
      }

      // Start syncing
      _updateStatus(SyncStatus.syncing);
      _totalItems = unsyncedItems.length;
      _syncedItems = 0;
      _failedItems = 0;

      // Process items in batches to avoid overloading Firestore
      const batchSize = 10;
      for (var i = 0; i < unsyncedItems.length; i += batchSize) {
        final end = (i + batchSize < unsyncedItems.length)
            ? i + batchSize
            : unsyncedItems.length;
        final batch = unsyncedItems.sublist(i, end);

        // Process batch
        await Future.wait(batch.map((item) => _syncItem(item)));

        // Update progress
        _updateStatus(SyncStatus.syncing);
      }

      // Update final status
      if (_failedItems > 0) {
        _updateStatus(SyncStatus.partialSync);
        return false;
      } else {
        _updateStatus(SyncStatus.completed);
        return true;
      }
    } catch (e) {
      debugPrint('Error during sync: $e');
      _updateStatus(SyncStatus.failed);
      return false;
    }
  }

  // Sync a single item
  Future<void> _syncItem(dynamic item) async {
    // Different item types have different position properties
    if (item is HivePipelineModel) {
      debugPrint("Pipeline with ${item.points.length} points");
    } else {
      debugPrint("Item Location: (${item.latitude}, ${item.longitude})");
    }

    try {
      // Handle different types of items
      if (item is HiveUserModel) {
        if (item.isDeleted) {
          // Delete from Firestore
          await _firestoreRepository.deleteElement(item.id);
          // If successful, permanently delete from local storage
          await HiveService.deleteUser(item.id, permanent: true);
          debugPrint(
              'Deleted user ${item.id} from Firestore and local storage');
        } else {
          // Update or create in Firestore
          await _syncUser(item);
          // Mark as synced
          item.markAsSynced();
          // Save the changes to the box
          await HiveService.saveUser(item);
          debugPrint('Synced user ${item.id} to Firestore');
        }
      } else if (item is HiveValveModel) {
        if (item.isDeleted) {
          await _firestoreRepository.deleteElement(item.id);
          await HiveService.deleteValve(item.id, permanent: true);
          debugPrint(
              'Deleted valve ${item.id} from Firestore and local storage');
        } else {
          await _syncValve(item);
          item.markAsSynced();
          // Save the changes to the box
          await HiveService.saveValve(item);
          debugPrint('Synced valve ${item.id} to Firestore');
        }
        /* TODO: Uncomment when HiveSmartMeterModel is implemented
      } else if (item is HiveSmartMeterModel) {
        if (item.isDeleted) {
          await _firestoreRepository.deleteElement(item.id);
          await HiveService.deleteSmartMeter(item.id, permanent: true);
          debugPrint(
              'Deleted smart meter ${item.id} from Firestore and local storage');
        } else {
          await _syncSmartMeter(item);
          item.markAsSynced();
          // Save the changes to the box
          await HiveService.saveSmartMeter(item);
          debugPrint('Synced smart meter ${item.id} to Firestore');
        }
      */
      } else if (item is HiveConnectorModel) {
        if (item.isDeleted) {
          await _firestoreRepository.deleteElement(item.id);
          await HiveService.deleteConnector(item.id, permanent: true);
          debugPrint(
              'Deleted connector ${item.id} from Firestore and local storage');
        } else {
          await _syncConnector(item);
          item.markAsSynced();
          // Save the changes to the box
          await HiveService.saveConnector(item);
          debugPrint('Synced connector ${item.id} to Firestore');
        }
      } else if (item is HivePipelineModel) {
        if (item.isDeleted) {
          await _firestoreRepository.deleteElement(item.id);
          await HiveService.deletePipeline(item.id, permanent: true);
          debugPrint(
              'Deleted pipeline ${item.id} from Firestore and local storage');
        } else {
          await _syncPipeline(item);
          item.markAsSynced();
          // Save the changes to the box
          await HiveService.savePipeline(item);
          debugPrint('Synced pipeline ${item.id} to Firestore');
        }
      }

      _syncedItems++;
    } catch (e) {
      // Use debugPrint instead of print in production code
      debugPrint('Error syncing item: $e');
      _failedItems++;
    }
  }

  // Sync a user
  Future<void> _syncUser(HiveUserModel user) async {
    // Check if user exists in elements collection
    final existsInElements = await _firestoreRepository.elementExists(user.id);

    // Check if user exists in user collection
    final existsInUserCollection =
        await _firestoreRepository.userExists(user.id);

    // Update or create in elements collection
    if (existsInElements) {
      // Update existing user in elements collection with minimal data
      debugPrint('Updating existing user ${user.id} in elements collection');

      // Update metadata
      await _firestoreRepository.updateElementMetadata(user.id, user.title, {
        // Include only the user ID for reference when clicking on the map
        'userId': user.id,
      });

      // Update position separately
      debugPrint(
          'Updating position for user ${user.id} to (${user.latitude}, ${user.longitude})');
      await _firestoreRepository.updateElementPosition(user.id, user.position);
    } else {
      // Create new user in elements collection with the same ID as in local storage
      // with only minimal required data (no metadata)
      debugPrint('Creating new user ${user.id} in elements collection');
      await _firestoreRepository.addElementWithId(
          user.id, 'user', user.position, user.areaId, user.title, {
        // Include only the user ID for reference when clicking on the map
        'userId': user.id,
      });
    }

    // Update or create in user collection
    /* if (existsInUserCollection) {
      // Update existing user in user collection
      debugPrint('Updating existing user ${user.id} in user collection');
      await _firestoreRepository.updateUserDetails(user.id, user.details);
    } else {
      // Create new user in user collection
      debugPrint('Creating new user ${user.id} in user collection');
      await _firestoreRepository.addUserDetails(user.id, user.details);
    }*/
  }

  // Sync a valve
  Future<void> _syncValve(HiveValveModel valve) async {
    // Check if valve exists in elements collection
    final existsInElements = await _firestoreRepository.elementExists(valve.id);

    // Check if valve exists in valves collection
    final existsInValveCollection =
        await _firestoreRepository.valveExists(valve.id);

    // Update or create in elements collection
    if (existsInElements) {
      // Update existing valve in elements collection
      debugPrint('Updating existing valve ${valve.id} in elements collection');

      // Update metadata
      await _firestoreRepository.updateElementMetadata(valve.id, valve.title,
          {'lastUpdated': valve.updatedAt.millisecondsSinceEpoch});

      // Update position separately
      debugPrint(
          'Updating position for valve ${valve.id} to (${valve.latitude}, ${valve.longitude})');
      await _firestoreRepository.updateElementPosition(
          valve.id, valve.position);
    } else {
      // Create new valve in elements collection with the same ID as in local storage
      debugPrint('Creating new valve ${valve.id} in elements collection');
      await _firestoreRepository.addElementWithId(
          valve.id, 'valve', valve.position, valve.areaId, valve.title, {
        'type': 'valve',
        'createdAt': valve.createdAt.millisecondsSinceEpoch,
        'lastUpdated': valve.updatedAt.millisecondsSinceEpoch
      });
    }

    // Update or create in valves collection
    if (existsInValveCollection) {
      // Update existing valve in valves collection
      debugPrint('Updating existing valve ${valve.id} in valves collection');
      final specifications = {
        'type': valve.type,
        'controlType': valve.controlType,
        'status': valve.status,
        'size': valve.size,
        'lastMaintenance': valve.lastMaintenance?.millisecondsSinceEpoch,
      };
      await _firestoreRepository.updateValveDetails(valve.id, specifications);
    } else {
      // Create new valve in valves collection
      debugPrint('Creating new valve ${valve.id} in valves collection');
      await _firestoreRepository.addValveDetails(
        valveId: valve.id,
        status: valve.status,
        type: valve.type,
        lastMaintainance: valve.lastMaintenance?.millisecondsSinceEpoch ?? 0,
        controlType: valve.controlType,
        size: valve.size,
      );
    }
  }

  /* TODO: Uncomment when HiveSmartMeterModel is implemented
  // Sync a smart meter
  Future<void> _syncSmartMeter(HiveSmartMeterModel smartMeter) async {
    // Check if smart meter exists in elements collection
    final existsInElements =
        await _firestoreRepository.elementExists(smartMeter.id);

    // Check if smart meter exists in smart_meters collection
    final existsInSmartMeterCollection =
        await _firestoreRepository.smartMeterExists(smartMeter.id);

    // Update or create in elements collection
    if (existsInElements) {
      // Update existing smart meter in elements collection
      debugPrint(
          'Updating existing smart meter ${smartMeter.id} in elements collection');

      // Update metadata
      await _firestoreRepository
          .updateElementMetadata(smartMeter.id, smartMeter.title, {
        'lastUpdated': smartMeter.updatedAt.millisecondsSinceEpoch,
        'status': smartMeter.status,
      });

      // Update position separately
      debugPrint(
          'Updating position for smart meter ${smartMeter.id} to (${smartMeter.latitude}, ${smartMeter.longitude})');
      await _firestoreRepository.updateElementPosition(
          smartMeter.id, smartMeter.position);
    } else {
      // Create new smart meter in elements collection with the same ID as in local storage
      debugPrint(
          'Creating new smart meter ${smartMeter.id} in elements collection');
      await _firestoreRepository.addElementWithId(smartMeter.id, 'smart_meter',
          smartMeter.position, smartMeter.areaId, smartMeter.title, {
        'type': 'smart_meter',
        'createdAt': smartMeter.createdAt.millisecondsSinceEpoch,
        'lastUpdated': smartMeter.updatedAt.millisecondsSinceEpoch,
        'status': smartMeter.status,
      });
    }

    // Update or create in smart_meters collection
    if (existsInSmartMeterCollection) {
      // Update existing smart meter in smart_meters collection
      debugPrint(
          'Updating existing smart meter ${smartMeter.id} in smart_meters collection');
      await _firestoreRepository.updateSmartMeterDetails(
          smartMeter.id, smartMeter.specifications);
    } else {
      // Create new smart meter in smart_meters collection
      debugPrint(
          'Creating new smart meter ${smartMeter.id} in smart_meters collection');
      await _firestoreRepository.addSmartMeterDetails(
          smartMeter.id, smartMeter.specifications);
    }
  }
  */

  // Sync a connector
  Future<void> _syncConnector(HiveConnectorModel connector) async {
    // Check if connector exists in elements collection
    final existsInElements =
        await _firestoreRepository.elementExists(connector.id);

    // Check if connector exists in connectors collection
    final existsInConnectorCollection =
        await _firestoreRepository.connectorExists(connector.id);

    // Update or create in elements collection
    if (existsInElements) {
      // Update existing connector in elements collection
      debugPrint(
          'Updating existing connector ${connector.id} in elements collection');
      await _firestoreRepository
          .updateElementMetadata(connector.id, connector.title, {
        'connectorType': connector.connectorType,
        'lastUpdated': connector.updatedAt.millisecondsSinceEpoch
      });

      // Update position separately
      debugPrint(
          'Updating position for connector ${connector.id} to (${connector.latitude}, ${connector.longitude})');
      await _firestoreRepository.updateElementPosition(
          connector.id, connector.position);
    } else {
      // Create new connector in elements collection with the same ID as in local storage
      debugPrint(
          'Creating new connector ${connector.id} in elements collection');
      await _firestoreRepository.addElementWithId(connector.id, 'connector',
          connector.position, connector.areaId, connector.title, {
        'type': 'connector',
        'connectorType': connector.connectorType,
        'createdAt': connector.createdAt.millisecondsSinceEpoch,
        'lastUpdated': connector.updatedAt.millisecondsSinceEpoch
      });
    }

    // Update or create in connectors collection
    if (existsInConnectorCollection) {
      // Update existing connector in connectors collection
      debugPrint(
          'Updating existing connector ${connector.id} in connectors collection');
      await _firestoreRepository.updateConnectorDetails(
          connector.id,
          connector.connectorType,
          connector.specifications,
          connector.connections);
    } else {
      // Create new connector in connectors collection
      debugPrint(
          'Creating new connector ${connector.id} in connectors collection');
      await _firestoreRepository.addConnectorDetails(
          connector.id, connector.connectorType, connector.specifications);
    }
  }

  // Sync a pipeline
  Future<void> _syncPipeline(HivePipelineModel pipeline) async {
    try {
      debugPrint('🔄 Starting to sync pipeline ${pipeline.id}');
      debugPrint(
          'Pipeline sync status: ${pipeline.isSync ? "Synced" : "Not synced"}');
      debugPrint('Pipeline points: ${pipeline.points.length}');
      debugPrint('Pipeline properties: ${pipeline.properties}');

      // Log the pipeline state specifically
      final state = pipeline.properties['state'] ?? 'Active';
      debugPrint('🔄 Pipeline state to sync: $state (from properties)');
      debugPrint('🔄 Pipeline updatedAt: ${pipeline.updatedAt}');

      // Check if pipeline exists in elements collection
      final existsInElements =
          await _firestoreRepository.elementExists(pipeline.id);
      debugPrint('Pipeline exists in elements collection: $existsInElements');

      // Check if pipeline exists in pipelines collection
      final existsInPipelineCollection =
          await _firestoreRepository.pipelineExists(pipeline.id);
      debugPrint(
          'Pipeline exists in pipelines collection: $existsInPipelineCollection');

      // Get the pipeline state (already logged above)
      debugPrint('🔄 Confirming pipeline state to sync: $state');

      // Get segment states from properties
      final segmentStates = pipeline.getSegmentStates();

      debugPrint(
          'Pipeline ${pipeline.id} has ${segmentStates.length} segment states');
      if (segmentStates.isNotEmpty) {
        debugPrint('Segment states: ${segmentStates.toString()}');
      }

      // Update or create in elements collection
      if (existsInElements) {
        // Update existing pipeline in elements collection
        debugPrint(
            'Updating existing pipeline ${pipeline.id} in elements collection');
        debugPrint('🔄 Setting state in elements collection to: $state');
        await _firestoreRepository.updatePipelineElement(
            pipeline.id, pipeline.latLngPoints,
            state: state);

        // Explicitly update the state in the elements collection
        debugPrint('🔄 Explicitly updating state in elements collection');
        await _firestoreRepository.updateElementState(pipeline.id, state);
      } else {
        // Create new pipeline in elements collection with the same ID as in local storage
        debugPrint(
            'Creating new pipeline ${pipeline.id} in elements collection');
        debugPrint('🔄 Setting state in new pipeline element to: $state');
        // Pass properties directly to addPipelineElementWithId to ensure they're added to both collections
        await _firestoreRepository.addPipelineElementWithId(
            pipeline.id, pipeline.latLngPoints, pipeline.areaId, pipeline.title,
            properties: pipeline.properties, state: state);
      }

      // Update or create in pipelines collection
      if (existsInPipelineCollection) {
        // Update existing pipeline in pipelines collection
        debugPrint(
            'Updating existing pipeline ${pipeline.id} in pipelines collection');

        // Always use the toFirestoreDetails method to ensure all data is included
        final pipelineDetails = pipeline.toFirestoreDetails();

        // Log the state in the pipeline details
        final detailsState = pipelineDetails['state'] as String? ?? 'Active';
        debugPrint('🔄 Pipeline state in details to sync: $detailsState');

        // Log the properties in the pipeline details
        if (pipelineDetails.containsKey('properties')) {
          final properties =
              pipelineDetails['properties'] as Map<String, dynamic>;
          final propertiesState = properties['state'] as String? ?? 'Active';
          debugPrint(
              '🔄 Pipeline state in properties to sync: $propertiesState');
        }

        debugPrint(
            'Syncing pipeline details with ${pipelineDetails['segments']?.length ?? 0} segments');

        // Update the pipeline details in Firestore
        await _firestoreRepository.updatePipelineDetailsWithFullData(
            pipeline.id, pipelineDetails);
      } else {
        // Create new pipeline in pipelines collection
        debugPrint(
            'Creating new pipeline ${pipeline.id} in pipelines collection');

        // Use the toFirestoreDetails method to ensure all data is included
        final pipelineDetails = pipeline.toFirestoreDetails();

        // Log the state in the pipeline details
        final detailsState = pipelineDetails['state'] as String? ?? 'Active';
        debugPrint('🔄 Pipeline state in details to sync: $detailsState');

        // Log the properties in the pipeline details
        if (pipelineDetails.containsKey('properties')) {
          final properties =
              pipelineDetails['properties'] as Map<String, dynamic>;
          final propertiesState = properties['state'] as String? ?? 'Active';
          debugPrint(
              '🔄 Pipeline state in properties to sync: $propertiesState');
        }

        // Add the pipeline details to Firestore
        await _firestoreRepository.addPipelineDetailsWithFullData(
            pipeline.id, pipelineDetails);
      }
    } catch (e) {
      debugPrint('❌ Error syncing pipeline: $e');
      rethrow;
    }
  }

  // Get sync statistics
  SyncStatistics getSyncStatistics() {
    return SyncStatistics(
      totalItems: _totalItems,
      syncedItems: _syncedItems,
      failedItems: _failedItems,
      status: _status,
    );
  }

  // Update sync status
  void _updateStatus(SyncStatus status) {
    debugPrint('SyncService status changing from $_status to $status');
    _status = status;
    _syncStatusController.add(status);
  }

  // Force a status update (useful for debugging)
  void forceStatusUpdate() {
    debugPrint('SyncService forcing status update: $_status');
    _updateStatusBasedOnUnsyncedItems();
  }

  // Check if there are any unsynced items
  Future<bool> hasUnsyncedItems() async {
    final unsyncedItems = HiveService.getAllUnsyncedItems();
    debugPrint('SyncService: Found ${unsyncedItems.length} unsynced items');
    return unsyncedItems.isNotEmpty;
  }

  // Update status based on unsynced items
  void _updateStatusBasedOnUnsyncedItems() {
    final unsyncedItems = HiveService.getAllUnsyncedItems();
    debugPrint('SyncService: Found ${unsyncedItems.length} unsynced items');

    if (unsyncedItems.isEmpty) {
      if (_status != SyncStatus.syncing) {
        _updateStatus(SyncStatus.upToDate);
      }
    } else {
      if (_status != SyncStatus.syncing && _status != SyncStatus.failed) {
        _updateStatus(SyncStatus.idle);
      }
    }
  }

  // Fetch new data from Firestore based on last update time
  Future<void> _fetchNewDataFromFirestore() async {
    try {
      debugPrint('Fetching new data from Firestore');

      // Get the latest update timestamp from local database
      DateTime? latestUpdateTime;

      // Check all local items for latest update time
      final users = HiveService.getAllUsers(includeDeleted: true);
      final valves = HiveService.getAllValves(includeDeleted: true);
      final connectors = HiveService.getAllConnectors(includeDeleted: true);
      final pipelines = HiveService.getAllPipelines(includeDeleted: true);

      // Function to safely check updatedAt for each model type
      void checkUpdateTime(DateTime itemUpdatedAt) {
        if (latestUpdateTime == null ||
            itemUpdatedAt.isAfter(latestUpdateTime!)) {
          latestUpdateTime = itemUpdatedAt;
        }
      }

      // Check each type separately
      for (final user in users) {
        checkUpdateTime(user.updatedAt);
      }
      for (final valve in valves) {
        checkUpdateTime(valve.updatedAt);
      }
      for (final connector in connectors) {
        checkUpdateTime(connector.updatedAt);
      }
      for (final pipeline in pipelines) {
        checkUpdateTime(pipeline.updatedAt);
      }

      // Fetch updated data from Firestore
      final timestamp = latestUpdateTime?.millisecondsSinceEpoch ?? 0;
      debugPrint('Fetching items updated after timestamp: $timestamp');

      // Fetch users
      final updatedUsers = await _firestoreRepository.getElementsUpdatedAfter(
        'user',
        timestamp,
      );

      for (final userDoc in updatedUsers) {
        final element = FirestoreElement.fromFirestore(userDoc);
        final hiveUser =
            _firestoreRepository.createHiveUserFromFirestore(element);
        await HiveService.saveUser(hiveUser, markAsSynced: true);
      }

      // Fetch valves
      final updatedValves = await _firestoreRepository.getElementsUpdatedAfter(
        'valve',
        timestamp,
      );
      for (final valveDoc in updatedValves) {
        final element = FirestoreElement.fromFirestore(valveDoc);
        final hiveValve =
            _firestoreRepository.createHiveValveFromFirestore(element);
        await HiveService.saveValve(hiveValve, markAsSynced: true);
      }

      // Fetch connectors
      final updatedConnectors =
          await _firestoreRepository.getElementsUpdatedAfter(
        'connector',
        timestamp,
      );
      for (final connectorDoc in updatedConnectors) {
        final element = FirestoreElement.fromFirestore(connectorDoc);
        final hiveConnector =
            _firestoreRepository.createHiveConnectorFromFirestore(element);
        await HiveService.saveConnector(hiveConnector, markAsSynced: true);
      }

      // Fetch pipelines
      final updatedPipelines =
          await _firestoreRepository.getPipelinesUpdatedAfter(
        timestamp,
      );
      for (final pipelineDoc in updatedPipelines) {
        final pipeline = FirestorePipeline.fromFirestore(pipelineDoc);
        final hivePipeline = await _firestoreRepository
            .createHivePipelineFromFirestore(pipeline);
        await HiveService.savePipeline(hivePipeline, markAsSynced: true);
      }

      debugPrint('Fetched and saved updates from server: '
          '${updatedUsers.length} users, '
          '${updatedValves.length} valves, '
          '${updatedConnectors.length} connectors, '
          '${updatedPipelines.length} pipelines');
    } catch (e) {
      debugPrint('Error fetching new data from Firestore: $e');
      rethrow;
    }
  }

  // Dispose
  void dispose() {
    _checkTimer?.cancel();
    _syncStatusController.close();
  }
}

// Sync status enum
enum SyncStatus {
  idle,
  syncing,
  completed,
  partialSync,
  failed,
  noConnection,
  upToDate,
}

// Sync statistics class
class SyncStatistics {
  final int totalItems;
  final int syncedItems;
  final int failedItems;
  final SyncStatus status;

  SyncStatistics({
    required this.totalItems,
    required this.syncedItems,
    required this.failedItems,
    required this.status,
  });

  double get progress => totalItems > 0 ? syncedItems / totalItems : 0.0;
  bool get isComplete => syncedItems == totalItems;
}
