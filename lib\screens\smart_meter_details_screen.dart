import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/hive_models/hive_smart_meter_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';
import '../utils/nepali_names.dart';

class SmartMeterDetailsScreen extends ConsumerStatefulWidget {
  final HiveSmartMeterModel smartMeter;

  const SmartMeterDetailsScreen({
    super.key,
    required this.smartMeter,
  });

  @override
  ConsumerState<SmartMeterDetailsScreen> createState() =>
      _SmartMeterDetailsScreenState();
}

class _SmartMeterDetailsScreenState
    extends ConsumerState<SmartMeterDetailsScreen> {
  late TextEditingController _titleController;
  late TextEditingController _typeController;
  late TextEditingController _readingController;
  late TextEditingController _priceController;
  late String _selectedStatus;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final smartMeter = widget.smartMeter;
    final specs = smartMeter.specifications;

    _titleController = TextEditingController(text: smartMeter.title);
    _typeController =
        TextEditingController(text: specs['type'] as String? ?? 'Standard');
    _readingController =
        TextEditingController(text: specs['reading'] as String? ?? '0');
    _priceController = TextEditingController(
      text: smartMeter.pricePerUnit?.toString() ?? '',
    );
    _selectedStatus = smartMeter.status;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _typeController.dispose();
    _readingController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  Future<void> _syncSmartMeter() async {
    final firestoreRepository = ref.read(firestoreRepositoryProvider);
    final hiveService = ref.read(hiveServiceProvider);

    // Create LatLng position
    final position =
        LatLng(widget.smartMeter.latitude, widget.smartMeter.longitude);

    // Save to Firestore elements collection with the correct parameters
    await firestoreRepository.addElement(
        'smartMeter',
        position,
        widget.smartMeter.areaId,
        widget.smartMeter.title,
        {'type': 'smartMeter'});

    // Save to Firestore smart_meters collection
    await firestoreRepository.addSmartMeterDetails(
        widget.smartMeter.id, widget.smartMeter.specifications);

    // Mark as synced in Hive
    widget.smartMeter.isSync = true;
    await hiveService.saveSmartMeter(widget.smartMeter);
  }

  Future<void> _saveChanges() async {
    final hiveService = ref.read(hiveServiceProvider);
    final firestoreRepository = ref.read(firestoreRepositoryProvider);

    // Update the smart meter
    widget.smartMeter.title = _titleController.text;
    widget.smartMeter.specifications = {
      'type': _typeController.text,
      'reading': _readingController.text,
    };
    widget.smartMeter.status = _selectedStatus;
    widget.smartMeter.updatedAt = DateTime.now();
    widget.smartMeter.isSync = false;

    // Update price per unit
    if (_priceController.text.isNotEmpty) {
      widget.smartMeter.pricePerUnit = double.tryParse(_priceController.text);
    } else {
      widget.smartMeter.pricePerUnit = null;
    }

    try {
      // Save to Hive
      await hiveService.saveSmartMeter(widget.smartMeter);

      // Save to Firestore if already synced
      if (widget.smartMeter.isSync) {
        // Update in Firestore elements collection
        await firestoreRepository.updateElementTitle(
            widget.smartMeter.id, widget.smartMeter.title);

        // Update in Firestore smart_meters collection
        await firestoreRepository.updateSmartMeterDetails(
            widget.smartMeter.id, widget.smartMeter.specifications);

        // Mark as synced in Hive
        widget.smartMeter.isSync = true;
        await hiveService.saveSmartMeter(widget.smartMeter);
      }

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show success message
      _showSnackBar(
          'Smart meter "${_titleController.text}" updated successfully');

      // Exit edit mode
      setState(() {
        _isEditing = false;
      });
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error message
      _showSnackBar('Error updating smart meter: $e', isError: true);
    }
  }

  Future<void> _deleteSmartMeter() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Smart Meter'),
        content: Text(
            'Are you sure you want to delete "${widget.smartMeter.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (confirmed != true || !mounted) return;

    try {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);

      // Mark as deleted in Hive
      final smartMeter = widget.smartMeter;
      smartMeter.isDeleted = true;
      smartMeter.isSync = false;
      await hiveService.saveSmartMeter(smartMeter);

      // Delete from Firestore if it was synced
      if (smartMeter.isSync) {
        await firestoreRepository.deleteElement(smartMeter.id);
      }

      // Permanently delete from Hive
      await hiveService.deleteSmartMeter(smartMeter.id, permanent: true);

      // Show success message
      if (!mounted) return;
      _showSnackBar('Smart meter "${smartMeter.title}" deleted successfully');

      // Navigate back
      Navigator.pop(context, true); // Pass true to indicate deletion
    } catch (e) {
      if (!mounted) return;
      _showSnackBar('Error deleting smart meter: $e', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Smart Meter' : 'Smart Meter Details'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            onPressed: _isEditing ? _saveChanges : _toggleEditMode,
          ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteSmartMeter,
            ),
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                setState(() {
                  // Refresh the data
                  final hiveService = ref.read(hiveServiceProvider);
                  final smartMeters = hiveService.getAllSmartMeters();
                  final updatedSmartMeter = smartMeters.firstWhere(
                    (m) => m.id == widget.smartMeter.id,
                    orElse: () => widget.smartMeter,
                  );

                  if (updatedSmartMeter != widget.smartMeter) {
                    // Update the widget's smart meter with the latest data
                    widget.smartMeter.title = updatedSmartMeter.title;
                    widget.smartMeter.specifications =
                        updatedSmartMeter.specifications;
                    widget.smartMeter.status = updatedSmartMeter.status;
                    widget.smartMeter.lastReading =
                        updatedSmartMeter.lastReading;
                    widget.smartMeter.isSync = updatedSmartMeter.isSync;
                    widget.smartMeter.updatedAt = updatedSmartMeter.updatedAt;
                  }
                });
              },
            ),
          if (!_isEditing)
            IconButton(
              icon: Icon(
                widget.smartMeter.isSync
                    ? Icons.cloud_done
                    : Icons.cloud_upload,
                color: widget.smartMeter.isSync ? Colors.green : Colors.orange,
              ),
              onPressed: () {
                if (widget.smartMeter.isSync) {
                  _showSnackBar('Smart meter is already synced');
                  return;
                }

                // Use a separate method to handle the async operations
                _syncSmartMeter().then((_) {
                  if (mounted) {
                    setState(() {}); // Refresh UI
                    _showSnackBar('Smart meter synced successfully');
                  }
                }).catchError((e) {
                  if (mounted) {
                    _showSnackBar('Error syncing smart meter: $e',
                        isError: true);
                  }
                });
              },
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: _isEditing ? _buildEditForm() : _buildDetailsView(),
      ),
    );
  }

  Widget _buildDetailsView() {
    final smartMeter = widget.smartMeter;
    final specs = smartMeter.specifications;

    // Determine color based on status
    Color statusColor;
    switch (smartMeter.status) {
      case 'Active':
        statusColor = Colors.green;
        break;
      case 'Inactive':
        statusColor = Colors.grey;
        break;
      case 'Maintenance':
        statusColor = Colors.orange;
        break;
      default:
        statusColor = Colors.black;
    }

    // Get a Nepali name for the smart meter
    final displayName =
        NepaliFriendlyNames.getNameByType('meter', smartMeter.title);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: ListTile(
            leading: Icon(Icons.speed, size: 40, color: statusColor),
            title: Text(displayName,
                style: Theme.of(context).textTheme.titleLarge),
            subtitle: Text('Type: ${specs['type'] ?? 'Standard'}'),
            trailing: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: statusColor.withAlpha(51), // 0.2 * 255 = 51
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                smartMeter.status,
                style:
                    TextStyle(color: statusColor, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        _buildDetailItem('Reading', specs['reading'] ?? '0'),
        _buildDetailItem(
            'Price Per Unit',
            smartMeter.pricePerUnit != null
                ? '${smartMeter.pricePerUnit!.toStringAsFixed(2)} NPR'
                : 'Not set'),
        _buildDetailItem('Location',
            'Lat: ${smartMeter.latitude.toStringAsFixed(6)}, Lng: ${smartMeter.longitude.toStringAsFixed(6)}'),
        _buildDetailItem('ID', smartMeter.id),
        _buildDetailItem('Created At',
            _formatTimestamp(smartMeter.createdAt.millisecondsSinceEpoch)),
        _buildDetailItem('Updated At',
            _formatTimestamp(smartMeter.updatedAt.millisecondsSinceEpoch)),
        if (smartMeter.lastReading != null)
          _buildDetailItem('Last Reading Date',
              _formatTimestamp(smartMeter.lastReading!.millisecondsSinceEpoch)),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
          const Divider(),
        ],
      ),
    );
  }

  String _formatTimestamp(int timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  Widget _buildEditForm() {
    return Form(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Smart Meter Name *',
              hintText: 'Enter smart meter name',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _typeController,
            decoration: const InputDecoration(
              labelText: 'Meter Type *',
              hintText: 'e.g., Standard, Digital, Analog',
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _readingController,
            decoration: const InputDecoration(
              labelText: 'Current Reading *',
              hintText: 'e.g., 1000',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _priceController,
            decoration: const InputDecoration(
              labelText: 'Price Per Unit (NPR)',
              hintText: 'e.g., 10.50',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedStatus,
            decoration: const InputDecoration(
              labelText: 'Status',
            ),
            items: const [
              DropdownMenuItem(value: 'Active', child: Text('Active')),
              DropdownMenuItem(value: 'Inactive', child: Text('Inactive')),
              DropdownMenuItem(
                  value: 'Maintenance', child: Text('Maintenance')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedStatus = value;
                });
              }
            },
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                    _initializeControllers(); // Reset to original values
                  });
                },
                icon: const Icon(Icons.cancel),
                label: const Text('Cancel'),
              ),
              ElevatedButton.icon(
                onPressed: _saveChanges,
                icon: const Icon(Icons.save),
                label: const Text('Save Changes'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
