import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'services/implementations/hive_service_impl.dart';
import 'providers/hive_service_provider.dart';
import 'home/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for local storage
  try {
    final hiveService = HiveServiceImpl();
    await hiveService.init();
    setHiveServiceInstance(hiveService);
    debugPrint('Hive initialized successfully');
  } catch (e) {
    debugPrint('Failed to initialize Hive: $e');
  }

  bool firebaseInitialized = false;

  try {
    // First try with default options
    await Firebase.initializeApp();
    firebaseInitialized = true;
  } catch (e) {
    debugPrint('Failed to initialize Firebase with options: $e');

    // Fallback to auto initialization
    try {
      await Firebase.initializeApp();
      firebaseInitialized = true;
      debugPrint('Firebase initialized with auto-detection');
    } catch (e) {
      debugPrint('Failed to initialize Firebase: $e');
    }
  }

  runApp(
    ProviderScope(
      child: MyApp(firebaseInitialized: firebaseInitialized),
    ),
  );
}

class MyApp extends StatelessWidget {
  final bool firebaseInitialized;

  const MyApp({Key? key, this.firebaseInitialized = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'GIS Pipeline Mapping',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: firebaseInitialized
          ? const HomeScreen()
          : const FirebaseErrorScreen(),
    );
  }
}

class FirebaseErrorScreen extends StatelessWidget {
  const FirebaseErrorScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Error'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 64),
              const SizedBox(height: 16),
              const Text(
                'Firebase Initialization Error',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text(
                'The app could not connect to Firebase. Please make sure you have:',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                '1. Added google-services.json to android/app/\n'
                '2. Configured Firebase properly\n'
                '3. Connected to the internet',
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // Retry initialization
                  main();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
