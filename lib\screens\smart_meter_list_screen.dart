import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/pipeline.dart';
import '../models/hive_models/hive_smart_meter_model.dart';
import '../home/<USER>';
import '../utils/nepali_names.dart';
import '../providers/hive_service_provider.dart';
import '../providers/sync_service_provider.dart';
import '../services/sync_service.dart';
import 'smart_meter_details_screen.dart';
import 'add_smart_meter_screen.dart';

class SmartMeterListScreen extends ConsumerStatefulWidget {
  const SmartMeterListScreen({super.key});

  @override
  ConsumerState<SmartMeterListScreen> createState() =>
      _SmartMeterListScreenState();
}

class _SmartMeterListScreenState extends ConsumerState<SmartMeterListScreen> {
  bool _isLoading = false;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    // Check if we need to fetch data from server
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndFetchData();
    });
  }

  Future<void> _checkAndFetchData() async {
    final hiveService = ref.read(hiveServiceProvider);
    final syncService = ref.read(syncServiceProvider);

    // Check if smart meter list is empty
    final smartMeters = hiveService.getAllSmartMeters();
    if (smartMeters.isEmpty) {
      setState(() {
        _isLoading = true;
      });

      // Fetch smart meters from server
      await syncService.fetchSmartMeterListing();

      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _syncWithServer() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = ref.read(syncServiceProvider);
      await syncService.syncSmartMeterListing();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Smart meters synced successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error syncing smart meters: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get smart meters from Hive
    final hiveService = ref.read(hiveServiceProvider);
    final smartMeters = hiveService.getAllSmartMeters();

    // Also get meters from map state for any that might not be in Hive yet
    final mapState = ref.watch(mapStateProvider);
    final mapMeters = mapState.userValvePositions
        .where((position) => position.type == PositionType.meter)
        .toList();

    // Create a combined list of all meters
    final List<dynamic> meters = [...smartMeters];

    // Add any map meters that aren't already in the list
    for (final mapMeter in mapMeters) {
      if (!smartMeters.any((m) => m.id == mapMeter.id)) {
        meters.add(mapMeter);
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text(NepaliFriendlyNames.smartMeterListTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Smart Meter',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddSmartMeterScreen(),
                ),
              ).then((refresh) {
                if (refresh == true) {
                  setState(() {});
                }
              });
            },
          ),
          // Sync button
          IconButton(
            icon: _isSyncing
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.sync),
            tooltip: 'Sync with server',
            onPressed: _isSyncing ? null : _syncWithServer,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              // Reload data from local database
              final mapNotifier = ref.read(mapStateProvider.notifier);
              mapNotifier.loadInitialData();
              setState(() {});
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Fetching smart meters from server...'),
                ],
              ),
            )
          : meters.isEmpty
              ? const Center(
                  child: Text('No smart meters found'),
                )
              : ListView.builder(
                  itemCount: meters.length,
                  itemBuilder: (context, index) {
                    final meter = meters[index];
                    HiveSmartMeterModel smartMeter;
                    String displayName;

                    // Check if the meter is already a HiveSmartMeterModel
                    if (meter is HiveSmartMeterModel) {
                      smartMeter = meter;
                      displayName = NepaliFriendlyNames.getNameByType(
                          'meter', smartMeter.title);
                    } else {
                      // It's a UserValvePosition from the map
                      displayName = NepaliFriendlyNames.getNameByType(
                          'meter', meter.title);

                      // Convert to HiveSmartMeterModel
                      smartMeter = HiveSmartMeterModel(
                        id: meter.id,
                        latitude: meter.position.latitude,
                        longitude: meter.position.longitude,
                        title: meter.title,
                        areaId: 'default',
                      );
                    }

                    // Get specifications
                    final specs = smartMeter.specifications;
                    final meterType = specs['type'] ?? 'Standard';
                    final meterSize = specs['size'] ?? 'N/A';
                    final reading = specs['reading'] ?? '0';

                    // Determine color based on status
                    Color statusColor;
                    switch (smartMeter.status) {
                      case 'Active':
                        statusColor = Colors.green;
                        break;
                      case 'Inactive':
                        statusColor = Colors.grey;
                        break;
                      case 'Maintenance':
                        statusColor = Colors.orange;
                        break;
                      default:
                        statusColor = Colors.black;
                    }

                    return ListTile(
                      leading: Icon(Icons.speed, color: statusColor),
                      title: Text(displayName),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                              'Type: $meterType | Size: $meterSize | Reading: $reading'),
                          Text(
                            'ID: ${meter.id.substring(0, 8)}...',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                      isThreeLine: true,
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // Navigate to smart meter details screen using the already retrieved smart meter
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SmartMeterDetailsScreen(
                              smartMeter: smartMeter,
                            ),
                          ),
                        ).then((deleted) {
                          // Refresh the UI when returning from the details screen
                          if (deleted == true) {
                            setState(() {});
                          }
                        });
                      },
                    );
                  },
                ),
    );
  }
}
