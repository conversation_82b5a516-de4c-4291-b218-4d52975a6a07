import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/hive_models/hive_valve_model.dart';
import '../providers/hive_service_provider.dart';
import '../providers/firestore_provider.dart';
import '../utils/nepali_names.dart';

class AddValveScreen extends ConsumerStatefulWidget {
  const AddValveScreen({super.key});

  @override
  ConsumerState<AddValveScreen> createState() => _AddValveScreenState();
}

class _AddValveScreenState extends ConsumerState<AddValveScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _priceController = TextEditingController();
  String _selectedValveType = 'Gate';
  String _selectedValveSize = '15mm';
  String _selectedStatus = 'Open';

  final List<String> _valveTypes = [
    'Gate',
    'Ball',
    'Globe',
    'Butterfly',
    'Check',
    'Pressure Relief',
    'Control',
    'Needle',
  ];

  final List<String> _valveSizes = [
    '15mm',
    '20mm',
    '25mm',
    '50mm',
    '80mm',
    '100mm',
    '150mm',
    '200mm',
    '250mm',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  Future<void> _saveValve() async {
    if (_formKey.currentState!.validate()) {
      final hiveService = ref.read(hiveServiceProvider);
      final firestoreRepository = ref.read(firestoreRepositoryProvider);
      
      // Create specifications map
      final specifications = <String, dynamic>{
        'type': _selectedValveType,
        'size': _selectedValveSize,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      };
      
      // Parse price per unit if provided
      double? pricePerUnit;
      if (_priceController.text.isNotEmpty) {
        pricePerUnit = double.tryParse(_priceController.text);
      }
      
      // Create a new valve model
      final valve = HiveValveModel(
        id: const Uuid().v4(),
        latitude: 0, // Default values, will be updated when placed on map
        longitude: 0,
        title: _nameController.text,
        specifications: specifications,
        status: _selectedStatus,
        areaId: 'default', // Default area, will be updated when placed on map
        pricePerUnit: pricePerUnit,
      );
      
      try {
        // Save to Hive
        await hiveService.saveValve(valve);
        
        // Save to Firestore
        await firestoreRepository.addValveDetails(
          valve.id, 
          valve.specifications
        );
        
        // Mark as synced in Hive
        valve.isSync = true;
        await hiveService.saveValve(valve);
        
        if (!mounted) return;
        
        // Show success message
        _showSnackBar('Valve "${_nameController.text}" added successfully');
        
        // Navigate back
        Navigator.pop(context, true); // Pass true to refresh the list
      } catch (e) {
        if (!mounted) return;
        _showSnackBar('Error adding valve: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Valve'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Valve Name *',
                  hintText: 'Enter valve name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a valve name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedValveType,
                decoration: const InputDecoration(
                  labelText: 'Valve Type *',
                ),
                items: _valveTypes.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedValveType = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a valve type';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedValveSize,
                decoration: const InputDecoration(
                  labelText: 'Valve Size *',
                ),
                items: _valveSizes.map((size) {
                  return DropdownMenuItem(
                    value: size,
                    child: Text(size),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedValveSize = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a valve size';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'Price Per Unit (NPR)',
                  hintText: 'e.g., 500.00',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                ),
                items: const [
                  DropdownMenuItem(value: 'Open', child: Text('Open')),
                  DropdownMenuItem(value: 'Closed', child: Text('Closed')),
                  DropdownMenuItem(value: 'Maintenance', child: Text('Maintenance')),
                  DropdownMenuItem(value: 'Damaged', child: Text('Damaged')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _saveValve,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                ),
                child: const Text('Add Valve'),
              ),
              const SizedBox(height: 16),
              const Text(
                'Note: This will add the valve to the database. You can later add it to the map from the map screen.',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
